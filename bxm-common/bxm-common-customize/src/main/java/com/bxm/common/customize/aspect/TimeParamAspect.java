package com.bxm.common.customize.aspect;

import com.bxm.common.customize.annotation.TimeField;
import com.bxm.common.customize.annotation.TimeParamHandler;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

@Aspect
@Component
public class TimeParamAspect {

    @Before("@annotation(timeParamHandler)") // 拦截标注了 @TimeParamHandler 的方法
    public void handleTimeParams(org.aspectj.lang.JoinPoint joinPoint, TimeParamHandler timeParamHandler) throws IllegalAccessException {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args != null) {
            for (Object arg : args) {
                if (arg != null) {
                    // 处理参数对象中的时间字段
                    processTimeFields(arg);
                }
            }
        }
    }

    private void processTimeFields(Object arg) throws IllegalAccessException {
        // 获取类的所有字段
        Field[] fields = arg.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(TimeField.class)) {
                field.setAccessible(true); // 设置字段可访问
                TimeField timeField = field.getAnnotation(TimeField.class);
                String type = timeField.type(); // 获取注解的类型属性

                if (field.getType() == String.class) {
                    String fieldValue = (String) field.get(arg);
                    if (!StringUtils.isEmpty(fieldValue)) {
                        // 根据注解类型处理字段值
                        if ("start".equals(type)) {
                            field.set(arg, fieldValue + " 00:00:00");
                        } else if ("end".equals(type)) {
                            field.set(arg, fieldValue + " 23:59:59");
                        }
                    }
                }
            }
        }
    }
}
