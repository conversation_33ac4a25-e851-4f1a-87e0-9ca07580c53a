package com.bxm.customer.controller;

import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 增值交付单Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valuedAddedDeliveryOrder")
@Api(tags = "增值交付单管理")
public class ValuedAddedDeliveryOrderController extends BaseController {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    /**
     * 新增或更新增值交付单
     *
     * @param orderVO 增值交付单VO
     * @return 操作结果
     */
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值交付单", notes = "支持新增和更新操作，根据ID或交付单编号自动判断")
    @Log(title = "Upsert value added delivery order", businessType = BusinessType.INSERT)
    public AjaxResult upsert(@Valid @RequestBody ValueAddedDeliveryOrderVO orderVO) {
        try {
            log.info("Upsert delivery order request: {}", orderVO.getCustomerName());
            // 调用服务层进行upsert操作
            ValueAddedDeliveryOrder result = valueAddedDeliveryOrderService.upsert(orderVO);
            // 转换为VO返回
            ValueAddedDeliveryOrderVO resultVO = new ValueAddedDeliveryOrderVO();
            BeanUtils.copyProperties(result, resultVO);
            log.info("Upsert delivery order success: {}", result.getDeliveryOrderNo());
            return success(resultVO);
        } catch (IllegalArgumentException e) {
            log.warn("Upsert delivery order validation failed: {}", e.getMessage());
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("Upsert delivery order failed for customer: {}", orderVO.getCustomerName(), e);
            return error("保存增值交付单失败");
        }
    }

    /**
     * 条件查询增值交付单
     * 所有条件在 service 层进行动态拼接
     */
    @GetMapping("/query")
    @ApiOperation(value = "增值交付单查询", notes = "按条件分页查询增值交付单；条件均为可选")
    @Log(title = "Query value added delivery order", businessType = BusinessType.OTHER)
    public TableDataInfo query(DeliveryOrderQuery query) {
        // 启动分页（从请求参数 pageNum/pageSize 注入）
        startPage();
        // 记录查询关键信息，避免日志过大
        log.info("Query delivery orders, deliveryOrderNo={}, customerName={}, itemTypeId={}, status={}",
                StringUtils.nvl(query.getDeliveryOrderNo(), ""),
                StringUtils.nvl(query.getCustomerName(), ""),
                query.getValueAddedItemTypeId(),
                StringUtils.nvl(query.getStatus(), ""));
        List<ValueAddedDeliveryOrderVO> list = valueAddedDeliveryOrderService.queryVO(query);
        return getDataTable(list);
    }

    /**
     * 根据交付单编号查询增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 查询结果
     */
    @GetMapping("/getByOrderNo/{deliveryOrderNo}")
    @ApiOperation(value = "根据交付单编号查询", notes = "根据交付单编号查询增值交付单详情")
    @Log(title = "Get delivery order by order number", businessType = BusinessType.OTHER)
    public AjaxResult getByOrderNo(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Query delivery order by order number: {}", deliveryOrderNo);
            ValueAddedDeliveryOrder order = valueAddedDeliveryOrderService.getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                return error("未找到对应的增值交付单");
            }
            ValueAddedDeliveryOrderVO resultVO = new ValueAddedDeliveryOrderVO();
            BeanUtils.copyProperties(order, resultVO);
            return success(resultVO);
        } catch (Exception e) {
            log.error("Query delivery order failed for order number: {}", deliveryOrderNo, e);
            return error("查询增值交付单失败");
        }
    }

    /**
     * 生成交付单编号
     * 编号规则：VAD + yyMMddHHmmsss + 3位随机码，总长度19位
     */
    @GetMapping("/genDeliveryOrderNo")
    @ApiOperation(value = "生成交付单编号", notes = "生成唯一的增值交付单编号，格式：VAD+时间戳到毫秒+随机码，总长度19位")
    @Log(title = "Generate delivery order number", businessType = BusinessType.OTHER)
    public AjaxResult genDeliveryOrderNo() {
        try {
            // 生成时间戳部分：yyMMddHHmmsss格式（13位）
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmsss"));
            // 生成3位随机码
            String randomCode = StringUtils.generateRandomCode(3);
            // 组合生成最终编号
            String deliveryOrderNo = "VAD" + timestamp + randomCode;
            log.info("Generated delivery order number: {}", deliveryOrderNo);
            return success(deliveryOrderNo);
        } catch (Exception e) {
            log.error("Failed to generate delivery order number", e);
            return error("生成交付单编号失败");
        }
    }
}
