spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 120.27.156.42:18848
        namespace: prod
        username: zbb
        password: cTcjZfNwAPRaPtMJFk2NPjB0cTkn3znS
      config:
        # 配置中心地址
        server-addr: 120.27.156.42:18848
        # 配置文件格式
        file-extension: yaml
        refresh-enabled: true
        namespace: prod
        group: bxm-thirdpart
        name: bxm-thirdpart
        username: zbb
        password: cTcjZfNwAPRaPtMJFk2NPjB0cTkn3znS
        extension-configs: # 此处加载分离的扩展配置，例如抽取数据库、mybats配置等
          - data-id: redis.yaml
            group: bxm-common
            refresh: true

          - data-id: datasource.yaml
            group: bxm-common
            refresh: true
