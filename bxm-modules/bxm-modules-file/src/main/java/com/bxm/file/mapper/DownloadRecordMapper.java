package com.bxm.file.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.file.bean.dto.QualityCheckingItemDTO;
import com.bxm.file.bean.dto.UserDTO;
import com.bxm.file.domain.DownloadRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 导出记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Mapper
public interface DownloadRecordMapper extends BaseMapper<DownloadRecord>
{

    List<UserDTO> selectAllUsers();

    List<QualityCheckingItemDTO> selectQualityCheckingItemByIds(@Param("qualityCheckingItemIds") String qualityCheckingItemIds);

    List<QualityCheckingItemDTO> selectAllQualityCheckingItem();
}
