package com.bxm.file.bean.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RpaCreateVO {

    @ApiModelProperty("rpa类型，1-医社保，2-个税（工资薪金），3-国税，4-收入，5-预认证")
    private Integer rpaType;

    @ApiModelProperty("账期，yyyyMM")
    private Integer period;

    @ApiModelProperty("申请部门id")
    private Long deptId;

    @ApiModelProperty("操作类型，1-申报，2-扣款")
    private Integer operType;

    @ApiModelProperty("批次号，最后一步保存时需要")
    private String batchNo;
}
