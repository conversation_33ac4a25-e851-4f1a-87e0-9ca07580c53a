package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerPeriodIncomeDTO {

    @ApiModelProperty("公司名称")
    @Excel(name = "公司名称")
    private String customerName;

    @ApiModelProperty("纳税识别号")
    @Excel(name = "纳税识别号")
    private String creditCode;

    @ApiModelProperty("全量发票开票金额")
    @Excel(name = "全量发票开票金额")
    private BigDecimal allTicketAmount;

    @ApiModelProperty("全量发票开票税额")
    @Excel(name = "全量发票开票税额")
    private BigDecimal allTicketTaxAmount;

    @ApiModelProperty("开票数据查询金额")
    @Excel(name = "开票数据查询金额")
    private BigDecimal ticketSearchAmount;

    @ApiModelProperty("开票数据查询税额")
    @Excel(name = "开票数据查询税额")
    private BigDecimal ticketSearchTaxAmount;

    @ApiModelProperty("开始取数时间")
    @Excel(name = "开票时间", width = 30, dateFormat = "yyyy/M/d H:m:s")
    private Date ticketTime;

    @ApiModelProperty("异常原因")
    @Excel(name = "异常原因")
    private String exceptionReason;

    private Long customerServiceId;
}
