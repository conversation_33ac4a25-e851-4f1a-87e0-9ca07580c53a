package com.bxm.file.bean.dto.rpa;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class RpaCheckResult {

    @ApiModelProperty("总文件数量")
    private Long totalFileCount;

    @ApiModelProperty("已完成文件数量")
    private Long completeFileCount;

    @ApiModelProperty("解析数据")
    private List<? extends RpaEnterpriseData> dataList;

    @ApiModelProperty("是否有异常数据")
    private Boolean hasException;

    @ApiModelProperty("是否解析完成")
    private Boolean isComplete;

    @ApiModelProperty("异常信息")
    private List<String> errorStatistics;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @ApiModelProperty("批次号")
    private String batchNo;

    private Integer operType;

    private Integer rpaType;

    private Integer period;

    private Long deptId;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getRpaType() {
        return rpaType;
    }

    public void setRpaType(Integer rpaType) {
        this.rpaType = rpaType;
    }

    public Integer getOperType() {
        return operType;
    }

    public void setOperType(Integer operType) {
        this.operType = operType;
    }

    public RpaCheckResult() {

    }

    public RpaCheckResult(Long totalFileCount, Long completeFileCount, List<? extends RpaEnterpriseData> dataList, Boolean hasException, Boolean isComplete, List<String> errorStatistics, String batchNo, Integer operType, Integer rpaType, Integer period, Long deptId) {
        this.totalFileCount = totalFileCount;
        this.completeFileCount = completeFileCount;
        this.dataList = dataList;
        this.hasException = hasException;
        this.isComplete = isComplete;
        this.errorStatistics = errorStatistics;
        this.batchNo = batchNo;
        this.operType = operType;
        this.rpaType = rpaType;
        this.period = period;
        this.deptId = deptId;
    }

    // Getters and Setters

    public Long getTotalFileCount() {
        return totalFileCount;
    }

    public void setTotalFileCount(Long totalFileCount) {
        this.totalFileCount = totalFileCount;
    }

    public Long getCompleteFileCount() {
        return completeFileCount;
    }

    public void setCompleteFileCount(Long completeFileCount) {
        this.completeFileCount = completeFileCount;
    }

    public List<? extends RpaEnterpriseData> getDataList() {
        return dataList;
    }

    public void setDataList(List<? extends RpaEnterpriseData> dataList) {
        this.dataList = dataList;
    }

    public Boolean getHasException() {
        return hasException;
    }

    public void setHasException(Boolean hasException) {
        this.hasException = hasException;
    }

    public Boolean getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(Boolean isComplete) {
        this.isComplete = isComplete;
    }

    public List<String> getErrorStatistics() {
        return errorStatistics;
    }

    public void setErrorStatistics(List<String> errorStatistics) {
        this.errorStatistics = errorStatistics;
    }
}
